# Clean Architecture Product API

A comprehensive Java 17 Spring Boot application demonstrating Clean Architecture principles with Ports and Adapters pattern. This application implements a complete CRUD API for product management while maintaining strict architectural boundaries and dependency inversion.

## 🏗️ Architecture Overview

This application follows Clean Architecture principles with four distinct layers:

### 1. Domain Layer (`domain/`)
- **Entities**: Core business objects with identity (`Product`, `ProductId`, `Category`)
- **Repository Interfaces**: Contracts for data persistence
- **Domain Services**: Business logic that doesn't fit within a single entity
- **Exceptions**: Domain-specific business rule violations

### 2. Application Layer (`application/`)
- **Use Cases**: Application business rules and orchestration
- **DTOs**: Data transfer objects for input/output
- **Ports**: Interfaces defining application boundaries (inbound/outbound)
- **Mappers**: Data transformation between layers

### 3. Adapter Layer (`adapter/`)
- **Inbound Adapters**: REST controllers, request/response handling
- **Outbound Adapters**: JPA repositories, external service clients
- **Mappers**: Layer-specific data transformations

### 4. Infrastructure Layer (`infrastructure/`)
- **Configuration**: Spring Boot setup, dependency injection
- **Cross-cutting Concerns**: Logging, monitoring, security

## 🚀 Features

- **Complete CRUD Operations**: Create, Read, Update, Delete products
- **Advanced Search**: Filter by name, category, price range
- **Business Rules**: Product validation, uniqueness constraints
- **Rich Domain Model**: Encapsulated business logic and invariants
- **Comprehensive Testing**: Unit tests, integration tests, domain tests
- **API Documentation**: OpenAPI/Swagger integration
- **Clean Architecture**: Proper dependency inversion and layer separation

## 🛠️ Technology Stack

- **Java 17** with modern features (records, pattern matching, switch expressions)
- **Spring Boot 3.2.0** with auto-configuration
- **Spring Data JPA** for persistence
- **H2 Database** (in-memory for simplicity)
- **Gradle** for build management
- **JUnit 5** and **Mockito** for testing
- **OpenAPI 3** for API documentation
- **SLF4J** for logging

## 📋 Prerequisites

- Java 17 or higher
- Gradle 8.5 or higher (or use included wrapper)

## 🏃‍♂️ Quick Start

### 1. Clone and Build

```bash
git clone <repository-url>
cd clean-architecture-product-api
./gradlew build
```

### 2. Run the Application

```bash
./gradlew bootRun
```

The application will start on `http://localhost:8080`

### 3. Access the API

- **API Base URL**: `http://localhost:8080/api`
- **Swagger UI**: `http://localhost:8080/api/swagger-ui.html`
- **H2 Console**: `http://localhost:8080/api/h2-console`
  - JDBC URL: `jdbc:h2:mem:productdb`
  - Username: `sa`
  - Password: `password`

## 📚 API Endpoints

### Products

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products` | Get all products (with optional filters) |
| GET | `/api/products/{id}` | Get product by ID |
| POST | `/api/products` | Create new product |
| PUT | `/api/products/{id}` | Update existing product |
| DELETE | `/api/products/{id}` | Delete product |

### Query Parameters

- `name`: Filter products by name (partial match)
- `category`: Filter products by category

### Example Requests

#### Create Product
```bash
curl -X POST http://localhost:8080/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "MacBook Pro M3",
    "description": "Latest MacBook Pro with M3 chip",
    "price": 1999.99,
    "category": "ELECTRONICS"
  }'
```

#### Get All Products
```bash
curl http://localhost:8080/api/products
```

#### Search Products by Name
```bash
curl "http://localhost:8080/api/products?name=MacBook"
```

#### Filter by Category
```bash
curl "http://localhost:8080/api/products?category=ELECTRONICS"
```

## 🧪 Testing

### Run All Tests
```bash
./gradlew test
```

### Run Specific Test Categories
```bash
# Unit tests only
./gradlew test --tests "*Test"

# Integration tests only
./gradlew test --tests "*IntegrationTest"
```

### Test Coverage
The application includes comprehensive tests:
- **Domain Tests**: Entity business rules and domain services
- **Application Tests**: Use case logic and mappings
- **Integration Tests**: End-to-end API functionality
- **Repository Tests**: Data persistence layer

## 📊 Sample Data

The application comes with pre-loaded sample data including:
- Electronics (iPhone, MacBook, Samsung Galaxy, etc.)
- Books (Clean Code, Design Patterns)
- Sports equipment (Nike, Adidas shoes)
- Home & Garden items
- And more across all categories

## 🏛️ Clean Architecture Benefits

This implementation demonstrates:

1. **Dependency Inversion**: Domain layer has no dependencies on outer layers
2. **Testability**: Each layer can be tested in isolation
3. **Flexibility**: Easy to swap implementations (database, web framework)
4. **Maintainability**: Clear separation of concerns
5. **Business Logic Protection**: Core business rules are isolated and protected

## 📁 Project Structure

```
src/
├── main/java/com/cleanarch/productapi/
│   ├── domain/                     # Core business logic
│   │   ├── model/entity/          # Business entities
│   │   ├── repository/            # Repository interfaces
│   │   ├── service/               # Domain services
│   │   └── exception/             # Domain exceptions
│   ├── application/               # Application business rules
│   │   ├── usecase/              # Use case implementations
│   │   ├── dto/                  # Data transfer objects
│   │   ├── port/                 # Ports (interfaces)
│   │   ├── mapper/               # Application mappers
│   │   └── exception/            # Application exceptions
│   ├── adapter/                  # Interface adapters
│   │   ├── inbound/rest/         # REST controllers
│   │   └── outbound/persistence/ # JPA repositories
│   ├── infrastructure/           # Framework configuration
│   │   └── config/              # Spring configuration
│   └── ProductApiApplication.java # Main application class
└── test/                         # Test mirror structure
```

## 🔧 Configuration

### Profiles

- **dev**: Development profile with detailed logging
- **prod**: Production profile with optimized settings
- **test**: Test profile for automated testing

### Database Configuration

The application uses H2 in-memory database by default. To use a different database:

1. Add the database dependency to `build.gradle`
2. Update `application.yml` with connection details
3. Modify the dialect in JPA configuration

## 🚀 Deployment

### Building for Production

```bash
./gradlew bootJar
```

The executable JAR will be created in `build/libs/`

### Running in Production

```bash
java -jar build/libs/clean-architecture-product-api-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
```

## 🤝 Contributing

1. Follow the established architecture patterns
2. Maintain layer boundaries and dependency rules
3. Add tests for new functionality
4. Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions or issues, please create an issue in the repository or contact the development team.
