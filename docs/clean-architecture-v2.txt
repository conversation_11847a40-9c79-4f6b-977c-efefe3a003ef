adotech-api-v2/
├── build.gradle
└── src/
    ├── main/
    │   ├── java/
    │   │   └── com/sura/adotechapi/
    │   │       ├── domain/                           // Core business logic layer
    │   │       │   ├── model/
    │   │       │   │   └── entity/                   // Business entities with identity
    │   │       │   ├── repository/                   // Domain repository interfaces
    │   │       │   ├── service/                      // Domain services
    │   │       │   └── exception/                    // Domain-specific exceptions
    │   │       │
    │   │       ├── application/                      // Application business rules
    │   │       │   ├── usecase/                      // Use case implementations
    │   │       │   ├── dto/
    │   │       │   │   ├── request/                  // Input DTOs
    │   │       │   │   └── response/                 // Output DTOs
    │   │       │   ├── service/                      // Application services
    │   │       │   ├── port/                         // Ports and adapters pattern
    │   │       │   │   ├── inbound/                  // Use case interfaces (driving ports)
    │   │       │   │   └── outbound/                 // Repository/external interfaces (driven ports)
    │   │       │   ├── mapper/                       // Application layer mappers
    │   │       │   └── exception/                    // Application-specific exceptions
    │   │       │
    │   │       ├── adapter/                          // Interface adapters layer
    │   │       │   ├── inbound/                      // Driving adapters (controllers, consumers)
    │   │       │   │   ├── rest/
    │   │       │   │   │   ├── controller/           // REST controllers
    │   │       │   │   │   ├── mapper/               // REST request/response mappers
    │   │       │   │   │   ├── exception/            // REST exception handlers
    │   │       │   │   │   └── validation/           // Request validation
    │   │       │   │   └── messaging/
    │   │       │   │       ├── consumer/             // Message consumers
    │   │       │   │       ├── mapper/               // Message mappers
    │   │       │   │       └── config/               // Messaging configuration
    │   │       │   │
    │   │       │   └── outbound/                     // Driven adapters (repositories, clients)
    │   │       │       ├── persistence/
    │   │       │       │   ├── entity/               // JPA entities
    │   │       │       │   ├── repository/           // JPA repository implementations
    │   │       │       │   ├── mapper/               // Entity-domain mappers
    │   │       │       │   └── config/               // Database configuration
    │   │       │       ├── messaging/
    │   │       │       │   ├── publisher/            // Message publishers
    │   │       │       │   ├── mapper/               // Message mappers
    │   │       │       │   └── config/               // Publisher configuration
    │   │       │       └── external/
    │   │       │           ├── client/               // External API clients
    │   │       │           ├── mapper/               // External API mappers
    │   │       │           ├── config/               // External service configuration
    │   │       │           └── exception/            // External service exceptions
    │   │       │
    │   │       ├── infrastructure/                   // Infrastructure layer
    │   │       │   ├── config/                       // Spring Boot configuration
    │   │       │   │   ├── database/                 // Database configuration
    │   │       │   │   ├── messaging/                // Messaging infrastructure config
    │   │       │   │   ├── web/                      // Web configuration
    │   │       │   │   └── application/              // General app configuration
    │   │       │   ├── security/                     // Security configuration
    │   │       │   │   ├── authentication/          // Authentication setup
    │   │       │   │   ├── authorization/           // Authorization rules
    │   │       │   │   └── filter/                  // Security filters
    │   │       │   ├── monitoring/                   // Observability
    │   │       │   │   ├── metrics/                 // Application metrics
    │   │       │   │   ├── logging/                 // Logging configuration
    │   │       │   │   └── health/                  // Health checks
    │   │       │   └── scheduling/                   // Scheduled tasks
    │   │       │
    │   │       └── shared/                           // Shared utilities and constants
    │   │           ├── util/                         // Common utilities
    │   │           ├── constant/                     // Application constants
    │   │           ├── annotation/                   // Custom annotations
    │   │           └── exception/                    // Base exception classes
    │   │
    │   └── resources/
    │       ├── application.yml                       // Main configuration
    │       ├── application-dev.yml                   // Development profile
    │       ├── application-prod.yml                  // Production profile
    │       ├── db/
    │       │   ├── migrations/                       // Database migrations
    │       │   └── data/                            // Test data scripts
    │       ├── static/                              // Static web resources
    │       ├── templates/                           // Template files
    │       └── messages/                            // Internationalization files
    │
    └── test/
        └── java/com/sura/adotechapi/
            ├── domain/                              // Domain layer tests
            │   ├── model/
            │   └── service/
            ├── application/                         // Application layer tests
            │   ├── usecase/
            │   └── service/
            ├── adapter/                            // Adapter layer tests
            │   ├── inbound/
            │   │   ├── rest/                       // Integration tests
            │   │   └── messaging/
            │   └── outbound/
            │       ├── persistence/                // Repository tests
            │       └── external/                   // External service tests
            ├── infrastructure/                     // Infrastructure tests
            └── integration/                        // End-to-end tests