package com.cleanarch.productapi.adapter.inbound.rest.controller;

import com.cleanarch.productapi.application.dto.request.CreateProductRequest;
import com.cleanarch.productapi.application.dto.request.UpdateProductRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for ProductController.
 * These tests verify the complete flow from HTTP request to database and back.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ProductControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    @Transactional
    void shouldCreateProductSuccessfully() throws Exception {
        // Given
        CreateProductRequest request = new CreateProductRequest(
                "Integration Test Product",
                "Test Description",
                new BigDecimal("99.99"),
                "ELECTRONICS"
        );
        
        // When & Then
        mockMvc.perform(post("/api/products")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Integration Test Product"))
                .andExpect(jsonPath("$.description").value("Test Description"))
                .andExpect(jsonPath("$.price").value(99.99))
                .andExpect(jsonPath("$.category").value("ELECTRONICS"))
                .andExpect(jsonPath("$.id").exists());
    }
    
    @Test
    void shouldReturnValidationErrorForInvalidProduct() throws Exception {
        // Given
        CreateProductRequest request = new CreateProductRequest(
                "", // Invalid empty name
                "Test Description",
                new BigDecimal("-10.00"), // Invalid negative price
                "ELECTRONICS"
        );
        
        // When & Then
        mockMvc.perform(post("/api/products")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.validationErrors").exists());
    }
    
    @Test
    void shouldGetAllProductsSuccessfully() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/products"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(15)); // From data.sql
    }
    
    @Test
    void shouldGetProductByIdSuccessfully() throws Exception {
        // When & Then (using product from data.sql)
        mockMvc.perform(get("/api/products/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("1"))
                .andExpect(jsonPath("$.name").value("iPhone 15 Pro"));
    }
    
    @Test
    void shouldReturnNotFoundForNonExistentProduct() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/products/999"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("Product Not Found"));
    }
    
    @Test
    @Transactional
    void shouldUpdateProductSuccessfully() throws Exception {
        // Given
        UpdateProductRequest request = new UpdateProductRequest(
                "Updated iPhone",
                "Updated Description",
                new BigDecimal("1099.99"),
                "ELECTRONICS"
        );
        
        // When & Then
        mockMvc.perform(put("/api/products/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Updated iPhone"))
                .andExpect(jsonPath("$.description").value("Updated Description"))
                .andExpect(jsonPath("$.price").value(1099.99));
    }
    
    @Test
    @Transactional
    void shouldDeleteProductSuccessfully() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/products/1"))
                .andExpect(status().isNoContent());
        
        // Verify product is deleted
        mockMvc.perform(get("/api/products/1"))
                .andExpect(status().isNotFound());
    }
    
    @Test
    void shouldSearchProductsByNameSuccessfully() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/products?name=iPhone"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("iPhone 15 Pro"));
    }
    
    @Test
    void shouldFilterProductsByCategorySuccessfully() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/products?category=ELECTRONICS"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(4)); // Electronics products from data.sql
    }
}
