package com.cleanarch.productapi.domain.service;

import com.cleanarch.productapi.domain.exception.DuplicateProductException;
import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.model.entity.ProductId;
import com.cleanarch.productapi.domain.repository.ProductRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ProductDomainService.
 * These tests verify the domain service business logic using mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class ProductDomainServiceTest {
    
    @Mock
    private ProductRepository productRepository;
    
    private ProductDomainService productDomainService;
    
    @BeforeEach
    void setUp() {
        productDomainService = new ProductDomainService(productRepository);
    }
    
    @Test
    void shouldValidateUniqueProductNameWhenNoExistingProducts() {
        // Given
        String productName = "New Product";
        when(productRepository.findByNameContaining(productName))
                .thenReturn(Collections.emptyList());
        
        // When & Then
        assertThatNoException()
                .isThrownBy(() -> productDomainService.validateUniqueProductName(productName));
        
        verify(productRepository).findByNameContaining(productName);
    }
    
    @Test
    void shouldThrowExceptionWhenProductNameAlreadyExists() {
        // Given
        String productName = "Existing Product";
        Product existingProduct = new Product(
                ProductId.of("1"), productName, "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS, null, null);
        
        when(productRepository.findByNameContaining(productName))
                .thenReturn(List.of(existingProduct));
        
        // When & Then
        assertThatThrownBy(() -> productDomainService.validateUniqueProductName(productName))
                .isInstanceOf(DuplicateProductException.class)
                .hasMessageContaining("A product with name 'Existing Product' already exists");
        
        verify(productRepository).findByNameContaining(productName);
    }
    
    @Test
    void shouldAllowSimilarButNotExactProductNames() {
        // Given
        String newProductName = "New Product";
        Product existingProduct = new Product(
                ProductId.of("1"), "New Product Pro", "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS, null, null);
        
        when(productRepository.findByNameContaining(newProductName))
                .thenReturn(List.of(existingProduct));
        
        // When & Then
        assertThatNoException()
                .isThrownBy(() -> productDomainService.validateUniqueProductName(newProductName));
        
        verify(productRepository).findByNameContaining(newProductName);
    }
    
    @Test
    void shouldApplyBulkDiscountToProductsInPriceRange() {
        // Given
        BigDecimal minPrice = new BigDecimal("50.00");
        BigDecimal maxPrice = new BigDecimal("150.00");
        BigDecimal discountPercentage = new BigDecimal("10");
        
        Product product1 = new Product(
                ProductId.of("1"), "Product 1", "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS, null, null);
        Product product2 = new Product(
                ProductId.of("2"), "Product 2", "Description", 
                new BigDecimal("120.00"), Category.BOOKS, null, null);
        
        List<Product> productsInRange = Arrays.asList(product1, product2);
        
        when(productRepository.findByPriceBetween(minPrice, maxPrice))
                .thenReturn(productsInRange);
        when(productRepository.save(any(Product.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));
        
        // When
        List<Product> result = productDomainService.applyBulkDiscount(minPrice, maxPrice, discountPercentage);
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(product1.getPrice()).isEqualTo(new BigDecimal("90.00"));
        assertThat(product2.getPrice()).isEqualTo(new BigDecimal("108.00"));
        
        verify(productRepository).findByPriceBetween(minPrice, maxPrice);
        verify(productRepository, times(2)).save(any(Product.class));
    }
    
    @Test
    void shouldCalculateAveragePriceCorrectly() {
        // Given
        Product product1 = new Product(
                ProductId.of("1"), "Product 1", "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS, null, null);
        Product product2 = new Product(
                ProductId.of("2"), "Product 2", "Description", 
                new BigDecimal("200.00"), Category.BOOKS, null, null);
        Product product3 = new Product(
                ProductId.of("3"), "Product 3", "Description", 
                new BigDecimal("300.00"), Category.CLOTHING, null, null);
        
        when(productRepository.findAll())
                .thenReturn(Arrays.asList(product1, product2, product3));
        
        // When
        BigDecimal averagePrice = productDomainService.calculateAveragePrice();
        
        // Then
        assertThat(averagePrice).isEqualTo(new BigDecimal("200.00"));
        verify(productRepository).findAll();
    }
    
    @Test
    void shouldReturnZeroAveragePriceWhenNoProducts() {
        // Given
        when(productRepository.findAll()).thenReturn(Collections.emptyList());
        
        // When
        BigDecimal averagePrice = productDomainService.calculateAveragePrice();
        
        // Then
        assertThat(averagePrice).isEqualTo(BigDecimal.ZERO);
        verify(productRepository).findAll();
    }
    
    @Test
    void shouldIdentifyPremiumElectronicsProduct() {
        // Given
        Product product = new Product(
                ProductId.of("1"), "iPhone", "Description", 
                new BigDecimal("500.00"), Category.ELECTRONICS, null, null);
        
        // When
        boolean isPremium = productDomainService.isPremiumProduct(product);
        
        // Then
        assertThat(isPremium).isTrue();
    }
    
    @Test
    void shouldIdentifyPremiumExpensiveProduct() {
        // Given
        Product product = new Product(
                ProductId.of("1"), "Expensive Book", "Description", 
                new BigDecimal("1500.00"), Category.BOOKS, null, null);
        
        // When
        boolean isPremium = productDomainService.isPremiumProduct(product);
        
        // Then
        assertThat(isPremium).isTrue();
    }
    
    @Test
    void shouldIdentifyNonPremiumProduct() {
        // Given
        Product product = new Product(
                ProductId.of("1"), "Regular Book", "Description", 
                new BigDecimal("25.00"), Category.BOOKS, null, null);
        
        // When
        boolean isPremium = productDomainService.isPremiumProduct(product);
        
        // Then
        assertThat(isPremium).isFalse();
    }
}
