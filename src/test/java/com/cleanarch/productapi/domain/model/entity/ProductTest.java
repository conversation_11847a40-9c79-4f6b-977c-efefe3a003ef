package com.cleanarch.productapi.domain.model.entity;

import com.cleanarch.productapi.domain.exception.InvalidProductException;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.*;

/**
 * Unit tests for the Product domain entity.
 * These tests verify the business rules and invariants of the Product entity.
 */
class ProductTest {
    
    @Test
    void shouldCreateValidProduct() {
        // Given
        String name = "Test Product";
        String description = "Test Description";
        BigDecimal price = new BigDecimal("99.99");
        Category category = Category.ELECTRONICS;
        
        // When
        Product product = new Product(name, description, price, category);
        
        // Then
        assertThat(product.getName()).isEqualTo(name);
        assertThat(product.getDescription()).isEqualTo(description);
        assertThat(product.getPrice()).isEqualTo(price);
        assertThat(product.getCategory()).isEqualTo(category);
        assertThat(product.getCreatedAt()).isNotNull();
        assertThat(product.getUpdatedAt()).isNotNull();
    }
    
    @Test
    void shouldThrowExceptionWhenNameIsNull() {
        // Given
        String name = null;
        String description = "Test Description";
        BigDecimal price = new BigDecimal("99.99");
        Category category = Category.ELECTRONICS;
        
        // When & Then
        assertThatThrownBy(() -> new Product(name, description, price, category))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Product name cannot be null or empty");
    }
    
    @Test
    void shouldThrowExceptionWhenNameIsEmpty() {
        // Given
        String name = "";
        String description = "Test Description";
        BigDecimal price = new BigDecimal("99.99");
        Category category = Category.ELECTRONICS;
        
        // When & Then
        assertThatThrownBy(() -> new Product(name, description, price, category))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Product name cannot be null or empty");
    }
    
    @Test
    void shouldThrowExceptionWhenPriceIsNull() {
        // Given
        String name = "Test Product";
        String description = "Test Description";
        BigDecimal price = null;
        Category category = Category.ELECTRONICS;
        
        // When & Then
        assertThatThrownBy(() -> new Product(name, description, price, category))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Product price cannot be null");
    }
    
    @Test
    void shouldThrowExceptionWhenPriceIsNegative() {
        // Given
        String name = "Test Product";
        String description = "Test Description";
        BigDecimal price = new BigDecimal("-10.00");
        Category category = Category.ELECTRONICS;
        
        // When & Then
        assertThatThrownBy(() -> new Product(name, description, price, category))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Product price cannot be negative");
    }
    
    @Test
    void shouldThrowExceptionWhenCategoryIsNull() {
        // Given
        String name = "Test Product";
        String description = "Test Description";
        BigDecimal price = new BigDecimal("99.99");
        Category category = null;
        
        // When & Then
        assertThatThrownBy(() -> new Product(name, description, price, category))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Product category cannot be null");
    }
    
    @Test
    void shouldUpdateProductDetails() {
        // Given
        Product product = new Product("Original Name", "Original Description", 
                new BigDecimal("50.00"), Category.BOOKS);
        
        String newName = "Updated Name";
        String newDescription = "Updated Description";
        BigDecimal newPrice = new BigDecimal("75.00");
        Category newCategory = Category.ELECTRONICS;
        
        // When
        product.updateDetails(newName, newDescription, newPrice, newCategory);
        
        // Then
        assertThat(product.getName()).isEqualTo(newName);
        assertThat(product.getDescription()).isEqualTo(newDescription);
        assertThat(product.getPrice()).isEqualTo(newPrice);
        assertThat(product.getCategory()).isEqualTo(newCategory);
    }
    
    @Test
    void shouldReturnTrueForExpensiveProduct() {
        // Given
        Product product = new Product("Expensive Product", "Description", 
                new BigDecimal("1500.00"), Category.ELECTRONICS);
        
        // When & Then
        assertThat(product.isExpensive()).isTrue();
    }
    
    @Test
    void shouldReturnFalseForInexpensiveProduct() {
        // Given
        Product product = new Product("Cheap Product", "Description", 
                new BigDecimal("50.00"), Category.BOOKS);
        
        // When & Then
        assertThat(product.isExpensive()).isFalse();
    }
    
    @Test
    void shouldApplyDiscountCorrectly() {
        // Given
        Product product = new Product("Test Product", "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS);
        BigDecimal discountPercentage = new BigDecimal("10");
        
        // When
        product.applyDiscount(discountPercentage);
        
        // Then
        assertThat(product.getPrice()).isEqualTo(new BigDecimal("90.00"));
    }
    
    @Test
    void shouldThrowExceptionForInvalidDiscount() {
        // Given
        Product product = new Product("Test Product", "Description", 
                new BigDecimal("100.00"), Category.ELECTRONICS);
        BigDecimal invalidDiscount = new BigDecimal("150");
        
        // When & Then
        assertThatThrownBy(() -> product.applyDiscount(invalidDiscount))
                .isInstanceOf(InvalidProductException.class)
                .hasMessageContaining("Discount percentage must be between 0 and 100");
    }
}
