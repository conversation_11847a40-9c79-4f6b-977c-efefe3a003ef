package com.cleanarch.productapi.application.usecase;

import com.cleanarch.productapi.application.dto.request.CreateProductRequest;
import com.cleanarch.productapi.application.dto.request.UpdateProductRequest;
import com.cleanarch.productapi.application.dto.response.ProductResponse;
import com.cleanarch.productapi.application.mapper.ProductMapper;
import com.cleanarch.productapi.application.port.outbound.ProductRepositoryPort;
import com.cleanarch.productapi.domain.exception.ProductNotFoundException;
import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.model.entity.ProductId;
import com.cleanarch.productapi.domain.service.ProductDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ProductUseCaseImpl.
 * These tests verify the application service logic using mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class ProductUseCaseImplTest {
    
    @Mock
    private ProductRepositoryPort productRepository;
    
    @Mock
    private ProductDomainService productDomainService;
    
    @Mock
    private ProductMapper productMapper;
    
    private ProductUseCaseImpl productUseCase;
    
    @BeforeEach
    void setUp() {
        productUseCase = new ProductUseCaseImpl(productRepository, productDomainService, productMapper);
    }
    
    @Test
    void shouldCreateProductSuccessfully() {
        // Given
        CreateProductRequest request = new CreateProductRequest(
                "Test Product", "Description", new BigDecimal("99.99"), "ELECTRONICS");
        
        Product domainProduct = new Product("Test Product", "Description", 
                new BigDecimal("99.99"), Category.ELECTRONICS);
        Product savedProduct = new Product(ProductId.of("1"), "Test Product", "Description", 
                new BigDecimal("99.99"), Category.ELECTRONICS, LocalDateTime.now(), LocalDateTime.now());
        ProductResponse response = new ProductResponse("1", "Test Product", "Description", 
                new BigDecimal("99.99"), "ELECTRONICS", "Electronics", true, 
                LocalDateTime.now(), LocalDateTime.now());
        
        when(productMapper.toDomain(request)).thenReturn(domainProduct);
        when(productRepository.save(domainProduct)).thenReturn(savedProduct);
        when(productMapper.toResponse(savedProduct, productDomainService)).thenReturn(response);
        
        // When
        ProductResponse result = productUseCase.createProduct(request);
        
        // Then
        assertThat(result).isEqualTo(response);
        verify(productDomainService).validateUniqueProductName("Test Product");
        verify(productMapper).toDomain(request);
        verify(productRepository).save(domainProduct);
        verify(productMapper).toResponse(savedProduct, productDomainService);
    }
    
    @Test
    void shouldGetProductByIdSuccessfully() {
        // Given
        String productId = "1";
        ProductId id = ProductId.of(productId);
        Product product = new Product(id, "Test Product", "Description", 
                new BigDecimal("99.99"), Category.ELECTRONICS, LocalDateTime.now(), LocalDateTime.now());
        ProductResponse response = new ProductResponse("1", "Test Product", "Description", 
                new BigDecimal("99.99"), "ELECTRONICS", "Electronics", true, 
                LocalDateTime.now(), LocalDateTime.now());
        
        when(productRepository.findById(id)).thenReturn(Optional.of(product));
        when(productMapper.toResponse(product, productDomainService)).thenReturn(response);
        
        // When
        ProductResponse result = productUseCase.getProductById(productId);
        
        // Then
        assertThat(result).isEqualTo(response);
        verify(productRepository).findById(id);
        verify(productMapper).toResponse(product, productDomainService);
    }
    
    @Test
    void shouldThrowExceptionWhenProductNotFound() {
        // Given
        String productId = "999";
        ProductId id = ProductId.of(productId);
        
        when(productRepository.findById(id)).thenReturn(Optional.empty());
        
        // When & Then
        assertThatThrownBy(() -> productUseCase.getProductById(productId))
                .isInstanceOf(ProductNotFoundException.class);
        
        verify(productRepository).findById(id);
        verifyNoInteractions(productMapper);
    }
    
    @Test
    void shouldGetAllProductsSuccessfully() {
        // Given
        Product product1 = new Product(ProductId.of("1"), "Product 1", "Description 1", 
                new BigDecimal("99.99"), Category.ELECTRONICS, LocalDateTime.now(), LocalDateTime.now());
        Product product2 = new Product(ProductId.of("2"), "Product 2", "Description 2", 
                new BigDecimal("149.99"), Category.BOOKS, LocalDateTime.now(), LocalDateTime.now());
        
        List<Product> products = Arrays.asList(product1, product2);
        
        ProductResponse response1 = new ProductResponse("1", "Product 1", "Description 1", 
                new BigDecimal("99.99"), "ELECTRONICS", "Electronics", true, 
                LocalDateTime.now(), LocalDateTime.now());
        ProductResponse response2 = new ProductResponse("2", "Product 2", "Description 2", 
                new BigDecimal("149.99"), "BOOKS", "Books", false, 
                LocalDateTime.now(), LocalDateTime.now());
        
        when(productRepository.findAll()).thenReturn(products);
        when(productMapper.toResponse(product1, productDomainService)).thenReturn(response1);
        when(productMapper.toResponse(product2, productDomainService)).thenReturn(response2);
        
        // When
        List<ProductResponse> result = productUseCase.getAllProducts();
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result).containsExactly(response1, response2);
        verify(productRepository).findAll();
        verify(productMapper, times(2)).toResponse(any(Product.class), eq(productDomainService));
    }
    
    @Test
    void shouldUpdateProductSuccessfully() {
        // Given
        String productId = "1";
        ProductId id = ProductId.of(productId);
        UpdateProductRequest request = new UpdateProductRequest(
                "Updated Product", "Updated Description", new BigDecimal("199.99"), "BOOKS");
        
        Product existingProduct = new Product(id, "Original Product", "Original Description", 
                new BigDecimal("99.99"), Category.ELECTRONICS, LocalDateTime.now(), LocalDateTime.now());
        Product updatedProduct = new Product(id, "Updated Product", "Updated Description", 
                new BigDecimal("199.99"), Category.BOOKS, LocalDateTime.now(), LocalDateTime.now());
        ProductResponse response = new ProductResponse("1", "Updated Product", "Updated Description", 
                new BigDecimal("199.99"), "BOOKS", "Books", false, 
                LocalDateTime.now(), LocalDateTime.now());
        
        when(productRepository.findById(id)).thenReturn(Optional.of(existingProduct));
        when(productRepository.save(any(Product.class))).thenReturn(updatedProduct);
        when(productMapper.toResponse(updatedProduct, productDomainService)).thenReturn(response);
        
        // When
        ProductResponse result = productUseCase.updateProduct(productId, request);
        
        // Then
        assertThat(result).isEqualTo(response);
        verify(productRepository).findById(id);
        verify(productDomainService).validateUniqueProductName("Updated Product");
        verify(productRepository).save(any(Product.class));
        verify(productMapper).toResponse(updatedProduct, productDomainService);
    }
    
    @Test
    void shouldDeleteProductSuccessfully() {
        // Given
        String productId = "1";
        ProductId id = ProductId.of(productId);
        
        when(productRepository.existsById(id)).thenReturn(true);
        when(productRepository.deleteById(id)).thenReturn(true);
        
        // When
        productUseCase.deleteProduct(productId);
        
        // Then
        verify(productRepository).existsById(id);
        verify(productRepository).deleteById(id);
    }
    
    @Test
    void shouldThrowExceptionWhenDeletingNonExistentProduct() {
        // Given
        String productId = "999";
        ProductId id = ProductId.of(productId);
        
        when(productRepository.existsById(id)).thenReturn(false);
        
        // When & Then
        assertThatThrownBy(() -> productUseCase.deleteProduct(productId))
                .isInstanceOf(ProductNotFoundException.class);
        
        verify(productRepository).existsById(id);
        verify(productRepository, never()).deleteById(any());
    }
    
    @Test
    void shouldSearchProductsByNameSuccessfully() {
        // Given
        String searchName = "Test";
        Product product = new Product(ProductId.of("1"), "Test Product", "Description", 
                new BigDecimal("99.99"), Category.ELECTRONICS, LocalDateTime.now(), LocalDateTime.now());
        ProductResponse response = new ProductResponse("1", "Test Product", "Description", 
                new BigDecimal("99.99"), "ELECTRONICS", "Electronics", true, 
                LocalDateTime.now(), LocalDateTime.now());
        
        when(productRepository.findByNameContaining(searchName)).thenReturn(List.of(product));
        when(productMapper.toResponse(product, productDomainService)).thenReturn(response);
        
        // When
        List<ProductResponse> result = productUseCase.searchProductsByName(searchName);
        
        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(response);
        verify(productRepository).findByNameContaining(searchName);
        verify(productMapper).toResponse(product, productDomainService);
    }
}
