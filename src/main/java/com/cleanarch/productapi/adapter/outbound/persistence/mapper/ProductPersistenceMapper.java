package com.cleanarch.productapi.adapter.outbound.persistence.mapper;

import com.cleanarch.productapi.adapter.outbound.persistence.entity.CategoryJpaEnum;
import com.cleanarch.productapi.adapter.outbound.persistence.entity.ProductJpaEntity;
import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.model.entity.ProductId;
import org.springframework.stereotype.Component;

/**
 * Mapper for converting between domain entities and JPA entities.
 * This maintains the separation between the domain layer and persistence layer
 * by handling the transformation of data structures at the persistence boundary.
 */
@Component
public class ProductPersistenceMapper {
    
    /**
     * Maps a Product domain entity to a ProductJpaEntity.
     * @param product the domain entity
     * @return the JPA entity
     */
    public ProductJpaEntity toJpaEntity(Product product) {
        if (product == null) {
            return null;
        }
        
        ProductJpaEntity jpaEntity = new ProductJpaEntity();
        
        // Set ID if it exists (for updates)
        if (product.getId() != null) {
            try {
                jpaEntity.setId(Long.valueOf(product.getId().value()));
            } catch (NumberFormatException e) {
                // Handle case where ID is not a valid Long (shouldn't happen in normal flow)
                jpaEntity.setId(null);
            }
        }
        
        jpaEntity.setName(product.getName());
        jpaEntity.setDescription(product.getDescription());
        jpaEntity.setPrice(product.getPrice());
        jpaEntity.setCategory(toJpaEnum(product.getCategory()));
        jpaEntity.setCreatedAt(product.getCreatedAt());
        jpaEntity.setUpdatedAt(product.getUpdatedAt());
        
        return jpaEntity;
    }
    
    /**
     * Maps a ProductJpaEntity to a Product domain entity.
     * @param jpaEntity the JPA entity
     * @return the domain entity
     */
    public Product toDomain(ProductJpaEntity jpaEntity) {
        if (jpaEntity == null) {
            return null;
        }
        
        ProductId productId = jpaEntity.getId() != null ? 
                ProductId.of(jpaEntity.getId()) : null;
        
        return new Product(
                productId,
                jpaEntity.getName(),
                jpaEntity.getDescription(),
                jpaEntity.getPrice(),
                toDomainEnum(jpaEntity.getCategory()),
                jpaEntity.getCreatedAt(),
                jpaEntity.getUpdatedAt()
        );
    }
    
    /**
     * Maps a Category domain enum to a CategoryJpaEnum.
     * @param category the domain enum
     * @return the JPA enum
     */
    public CategoryJpaEnum toJpaEnum(Category category) {
        if (category == null) {
            return CategoryJpaEnum.OTHER;
        }
        
        return switch (category) {
            case ELECTRONICS -> CategoryJpaEnum.ELECTRONICS;
            case CLOTHING -> CategoryJpaEnum.CLOTHING;
            case BOOKS -> CategoryJpaEnum.BOOKS;
            case HOME_GARDEN -> CategoryJpaEnum.HOME_GARDEN;
            case SPORTS -> CategoryJpaEnum.SPORTS;
            case TOYS -> CategoryJpaEnum.TOYS;
            case AUTOMOTIVE -> CategoryJpaEnum.AUTOMOTIVE;
            case HEALTH_BEAUTY -> CategoryJpaEnum.HEALTH_BEAUTY;
            case FOOD_BEVERAGES -> CategoryJpaEnum.FOOD_BEVERAGES;
            case OTHER -> CategoryJpaEnum.OTHER;
        };
    }
    
    /**
     * Maps a CategoryJpaEnum to a Category domain enum.
     * @param jpaEnum the JPA enum
     * @return the domain enum
     */
    public Category toDomainEnum(CategoryJpaEnum jpaEnum) {
        if (jpaEnum == null) {
            return Category.OTHER;
        }
        
        return switch (jpaEnum) {
            case ELECTRONICS -> Category.ELECTRONICS;
            case CLOTHING -> Category.CLOTHING;
            case BOOKS -> Category.BOOKS;
            case HOME_GARDEN -> Category.HOME_GARDEN;
            case SPORTS -> Category.SPORTS;
            case TOYS -> Category.TOYS;
            case AUTOMOTIVE -> Category.AUTOMOTIVE;
            case HEALTH_BEAUTY -> Category.HEALTH_BEAUTY;
            case FOOD_BEVERAGES -> Category.FOOD_BEVERAGES;
            case OTHER -> Category.OTHER;
        };
    }
}
