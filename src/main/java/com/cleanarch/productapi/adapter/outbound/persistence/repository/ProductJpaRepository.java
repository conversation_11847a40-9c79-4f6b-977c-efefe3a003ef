package com.cleanarch.productapi.adapter.outbound.persistence.repository;

import com.cleanarch.productapi.adapter.outbound.persistence.entity.CategoryJpaEnum;
import com.cleanarch.productapi.adapter.outbound.persistence.entity.ProductJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Spring Data JPA repository for ProductJpaEntity.
 * This provides the basic CRUD operations and custom query methods for product persistence.
 */
@Repository
public interface ProductJpaRepository extends JpaRepository<ProductJpaEntity, Long> {
    
    /**
     * Find products by category.
     * @param category the product category
     * @return list of products in the specified category
     */
    List<ProductJpaEntity> findByCategory(CategoryJpaEnum category);
    
    /**
     * Find products by name containing the specified text (case-insensitive).
     * @param name the name to search for
     * @return list of products with names containing the specified text
     */
    List<ProductJpaEntity> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find products with price between the specified range.
     * @param minPrice minimum price (inclusive)
     * @param maxPrice maximum price (inclusive)
     * @return list of products within the price range
     */
    List<ProductJpaEntity> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Check if a product exists with the given name (case-insensitive).
     * @param name the product name
     * @return true if a product with the name exists, false otherwise
     */
    boolean existsByNameIgnoreCase(String name);
    
    /**
     * Find products ordered by price ascending.
     * @return list of products ordered by price
     */
    List<ProductJpaEntity> findAllByOrderByPriceAsc();
    
    /**
     * Find products ordered by creation date descending.
     * @return list of products ordered by creation date (newest first)
     */
    List<ProductJpaEntity> findAllByOrderByCreatedAtDesc();
    
    /**
     * Find products with price greater than the specified amount.
     * @param price the minimum price
     * @return list of products with price greater than the specified amount
     */
    @Query("SELECT p FROM ProductJpaEntity p WHERE p.price > :price")
    List<ProductJpaEntity> findProductsWithPriceGreaterThan(@Param("price") BigDecimal price);
    
    /**
     * Count products by category.
     * @param category the product category
     * @return count of products in the specified category
     */
    long countByCategory(CategoryJpaEnum category);
}
