package com.cleanarch.productapi.adapter.outbound.persistence.repository;

import com.cleanarch.productapi.adapter.outbound.persistence.mapper.ProductPersistenceMapper;
import com.cleanarch.productapi.application.port.outbound.ProductRepositoryPort;
import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.model.entity.ProductId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Adapter implementation of ProductRepositoryPort using JPA.
 * This is an outbound adapter that implements the repository port defined in the application layer.
 * It translates between domain entities and JPA entities using mappers.
 */
@Component
public class ProductRepositoryAdapter implements ProductRepositoryPort {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductRepositoryAdapter.class);
    
    private final ProductJpaRepository jpaRepository;
    private final ProductPersistenceMapper mapper;
    
    public ProductRepositoryAdapter(ProductJpaRepository jpaRepository, ProductPersistenceMapper mapper) {
        this.jpaRepository = jpaRepository;
        this.mapper = mapper;
    }
    
    @Override
    public Product save(Product product) {
        logger.debug("Saving product: {}", product.getName());
        
        var jpaEntity = mapper.toJpaEntity(product);
        var savedEntity = jpaRepository.save(jpaEntity);
        
        logger.debug("Product saved with ID: {}", savedEntity.getId());
        
        return mapper.toDomain(savedEntity);
    }
    
    @Override
    public Optional<Product> findById(ProductId id) {
        logger.debug("Finding product by ID: {}", id.value());
        
        try {
            Long jpaId = Long.valueOf(id.value());
            return jpaRepository.findById(jpaId)
                    .map(mapper::toDomain);
        } catch (NumberFormatException e) {
            logger.warn("Invalid product ID format: {}", id.value());
            return Optional.empty();
        }
    }
    
    @Override
    public List<Product> findAll() {
        logger.debug("Finding all products");
        
        return jpaRepository.findAll()
                .stream()
                .map(mapper::toDomain)
                .toList();
    }
    
    @Override
    public List<Product> findByCategory(Category category) {
        logger.debug("Finding products by category: {}", category);
        
        var jpaCategory = mapper.toJpaEnum(category);
        return jpaRepository.findByCategory(jpaCategory)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }
    
    @Override
    public List<Product> findByNameContaining(String name) {
        logger.debug("Finding products by name containing: {}", name);
        
        return jpaRepository.findByNameContainingIgnoreCase(name)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }
    
    @Override
    public List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice) {
        logger.debug("Finding products with price between {} and {}", minPrice, maxPrice);
        
        return jpaRepository.findByPriceBetween(minPrice, maxPrice)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }
    
    @Override
    public boolean deleteById(ProductId id) {
        logger.debug("Deleting product by ID: {}", id.value());
        
        try {
            Long jpaId = Long.valueOf(id.value());
            if (jpaRepository.existsById(jpaId)) {
                jpaRepository.deleteById(jpaId);
                logger.debug("Product deleted with ID: {}", id.value());
                return true;
            } else {
                logger.debug("Product not found for deletion with ID: {}", id.value());
                return false;
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid product ID format for deletion: {}", id.value());
            return false;
        }
    }
    
    @Override
    public boolean existsById(ProductId id) {
        logger.debug("Checking if product exists by ID: {}", id.value());
        
        try {
            Long jpaId = Long.valueOf(id.value());
            return jpaRepository.existsById(jpaId);
        } catch (NumberFormatException e) {
            logger.warn("Invalid product ID format: {}", id.value());
            return false;
        }
    }
    
    @Override
    public long count() {
        logger.debug("Counting all products");
        
        return jpaRepository.count();
    }
}
