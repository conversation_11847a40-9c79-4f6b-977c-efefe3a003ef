package com.cleanarch.productapi.adapter.inbound.rest.exception;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Error response structure for validation errors.
 * This extends the standard error response to include field-specific validation errors.
 */
public record ValidationErrorResponse(
        int status,
        String error,
        String message,
        String path,
        LocalDateTime timestamp,
        Map<String, String> validationErrors
) {
}
