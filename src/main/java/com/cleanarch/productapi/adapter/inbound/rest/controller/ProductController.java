package com.cleanarch.productapi.adapter.inbound.rest.controller;

import com.cleanarch.productapi.application.dto.request.CreateProductRequest;
import com.cleanarch.productapi.application.dto.request.UpdateProductRequest;
import com.cleanarch.productapi.application.dto.response.ProductResponse;
import com.cleanarch.productapi.application.port.inbound.ProductUseCase;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for product operations.
 * This is an inbound adapter that translates HTTP requests into application use case calls.
 * It follows the Ports and Adapters pattern by depending on the ProductUseCase port.
 */
@RestController
@RequestMapping("/products")
@Tag(name = "Products", description = "Product management operations")
public class ProductController {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductController.class);
    
    private final ProductUseCase productUseCase;
    
    public ProductController(ProductUseCase productUseCase) {
        this.productUseCase = productUseCase;
    }
    
    @PostMapping
    @Operation(summary = "Create a new product", description = "Creates a new product with the provided details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Product created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    public ResponseEntity<ProductResponse> createProduct(
            @Valid @RequestBody CreateProductRequest request) {
        
        logger.info("Received request to create product: {}", request.name());
        
        ProductResponse response = productUseCase.createProduct(request);
        
        logger.info("Product created successfully with ID: {}", response.id());
        
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get product by ID", description = "Retrieves a product by its unique identifier")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Product found"),
            @ApiResponse(responseCode = "404", description = "Product not found")
    })
    public ResponseEntity<ProductResponse> getProductById(
            @Parameter(description = "Product ID") @PathVariable String id) {
        
        logger.debug("Received request to get product with ID: {}", id);
        
        ProductResponse response = productUseCase.getProductById(id);
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @Operation(summary = "Get all products", description = "Retrieves all products with optional filtering")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Products retrieved successfully")
    })
    public ResponseEntity<List<ProductResponse>> getAllProducts(
            @Parameter(description = "Filter by product name") @RequestParam(required = false) String name,
            @Parameter(description = "Filter by category") @RequestParam(required = false) String category) {
        
        logger.debug("Received request to get all products with filters - name: {}, category: {}", name, category);
        
        List<ProductResponse> responses;
        
        if (name != null && !name.trim().isEmpty()) {
            responses = productUseCase.searchProductsByName(name);
        } else if (category != null && !category.trim().isEmpty()) {
            responses = productUseCase.getProductsByCategory(category);
        } else {
            responses = productUseCase.getAllProducts();
        }
        
        logger.debug("Retrieved {} products", responses.size());
        
        return ResponseEntity.ok(responses);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update product", description = "Updates an existing product with the provided details")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Product updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "404", description = "Product not found"),
            @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    public ResponseEntity<ProductResponse> updateProduct(
            @Parameter(description = "Product ID") @PathVariable String id,
            @Valid @RequestBody UpdateProductRequest request) {
        
        logger.info("Received request to update product with ID: {}", id);
        
        ProductResponse response = productUseCase.updateProduct(id, request);
        
        logger.info("Product updated successfully with ID: {}", id);
        
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete product", description = "Deletes a product by its unique identifier")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Product deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Product not found")
    })
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "Product ID") @PathVariable String id) {
        
        logger.info("Received request to delete product with ID: {}", id);
        
        productUseCase.deleteProduct(id);
        
        logger.info("Product deleted successfully with ID: {}", id);
        
        return ResponseEntity.noContent().build();
    }
}
