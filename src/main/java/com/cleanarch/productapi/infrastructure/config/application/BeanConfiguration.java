package com.cleanarch.productapi.infrastructure.config.application;

import com.cleanarch.productapi.application.port.outbound.ProductRepositoryPort;
import com.cleanarch.productapi.domain.repository.ProductRepository;
import com.cleanarch.productapi.domain.service.ProductDomainService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring configuration for application beans.
 * This configuration class wires together the different layers of the Clean Architecture,
 * ensuring proper dependency injection while maintaining the dependency inversion principle.
 */
@Configuration
public class BeanConfiguration {
    
    /**
     * Creates a ProductDomainService bean.
     * The domain service depends on the repository interface, which will be implemented
     * by the infrastructure layer.
     *
     * @param productRepository the repository implementation
     * @return configured ProductDomainService
     */
    @Bean
    public ProductDomainService productDomainService(ProductRepository productRepository) {
        return new ProductDomainService(productRepository);
    }
    
    /**
     * Creates a ProductRepository bean that delegates to the repository port.
     * This allows the domain layer to depend on its own repository interface
     * while the actual implementation is provided by the infrastructure layer.
     * 
     * @param productRepositoryPort the repository port implementation
     * @return ProductRepository implementation
     */
    @Bean
    public ProductRepository productRepository(ProductRepositoryPort productRepositoryPort) {
        return new ProductRepository() {
            @Override
            public com.cleanarch.productapi.domain.model.entity.Product save(
                    com.cleanarch.productapi.domain.model.entity.Product product) {
                return productRepositoryPort.save(product);
            }
            
            @Override
            public java.util.Optional<com.cleanarch.productapi.domain.model.entity.Product> findById(
                    com.cleanarch.productapi.domain.model.entity.ProductId id) {
                return productRepositoryPort.findById(id);
            }
            
            @Override
            public java.util.List<com.cleanarch.productapi.domain.model.entity.Product> findAll() {
                return productRepositoryPort.findAll();
            }
            
            @Override
            public java.util.List<com.cleanarch.productapi.domain.model.entity.Product> findByCategory(
                    com.cleanarch.productapi.domain.model.entity.Category category) {
                return productRepositoryPort.findByCategory(category);
            }
            
            @Override
            public java.util.List<com.cleanarch.productapi.domain.model.entity.Product> findByNameContaining(
                    String name) {
                return productRepositoryPort.findByNameContaining(name);
            }
            
            @Override
            public java.util.List<com.cleanarch.productapi.domain.model.entity.Product> findByPriceBetween(
                    java.math.BigDecimal minPrice, java.math.BigDecimal maxPrice) {
                return productRepositoryPort.findByPriceBetween(minPrice, maxPrice);
            }
            
            @Override
            public boolean deleteById(com.cleanarch.productapi.domain.model.entity.ProductId id) {
                return productRepositoryPort.deleteById(id);
            }
            
            @Override
            public boolean existsById(com.cleanarch.productapi.domain.model.entity.ProductId id) {
                return productRepositoryPort.existsById(id);
            }
            
            @Override
            public long count() {
                return productRepositoryPort.count();
            }
        };
    }
}
