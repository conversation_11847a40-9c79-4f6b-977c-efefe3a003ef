package com.cleanarch.productapi.infrastructure.config.web;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI (Swagger) configuration for API documentation.
 * This configures the API documentation that will be available at /swagger-ui.html
 */
@Configuration
public class OpenApiConfiguration {
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Clean Architecture Product API")
                        .description("A Spring Boot application demonstrating Clean Architecture principles with a Product CRUD API")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("Clean Architecture Team")
                                .email("<EMAIL>")
                                .url("https://github.com/cleanarch/product-api"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:8080")
                                .description("Development server"),
                        new Server()
                                .url("https://api.cleanarch.com")
                                .description("Production server")
                ));
    }
}
