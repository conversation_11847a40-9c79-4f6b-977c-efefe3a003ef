package com.cleanarch.productapi.infrastructure.config.database;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Database configuration for the application.
 * This configures JPA settings, entity scanning, and transaction management.
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.cleanarch.productapi.adapter.outbound.persistence.repository")
@EntityScan(basePackages = "com.cleanarch.productapi.adapter.outbound.persistence.entity")
@EnableJpaAuditing
@EnableTransactionManagement
public class DatabaseConfiguration {
    
    // Additional database configuration can be added here if needed
    // For example: custom DataSource configuration, connection pool settings, etc.
}
