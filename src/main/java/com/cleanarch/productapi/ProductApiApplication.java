package com.cleanarch.productapi;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Main Spring Boot application class for the Clean Architecture Product API.
 * 
 * This application demonstrates Clean Architecture principles with:
 * - Domain layer: Core business logic and entities
 * - Application layer: Use cases and application services
 * - Adapter layer: Controllers, repositories, and external interfaces
 * - Infrastructure layer: Framework configuration and cross-cutting concerns
 */
@SpringBootApplication
public class ProductApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProductApiApplication.class, args);
    }
}
