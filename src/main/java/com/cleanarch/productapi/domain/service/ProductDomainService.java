package com.cleanarch.productapi.domain.service;

import com.cleanarch.productapi.domain.exception.DuplicateProductException;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.repository.ProductRepository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Domain service for Product-related business operations.
 * This service contains business logic that doesn't naturally fit within a single entity
 * and may require coordination between multiple domain objects or repositories.
 */
public class ProductDomainService {
    
    private final ProductRepository productRepository;
    
    public ProductDomainService(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }
    
    /**
     * Validates that a product name is unique before creation.
     * This is a business rule that requires checking against existing products.
     * 
     * @param productName the name to validate
     * @throws DuplicateProductException if a product with the same name already exists
     */
    public void validateUniqueProductName(String productName) {
        List<Product> existingProducts = productRepository.findByNameContaining(productName);
        
        boolean duplicateExists = existingProducts.stream()
                .anyMatch(product -> product.getName().equalsIgnoreCase(productName.trim()));
        
        if (duplicateExists) {
            throw new DuplicateProductException("A product with name '" + productName + "' already exists");
        }
    }
    
    /**
     * Applies bulk discount to products within a price range.
     * This is a complex business operation that affects multiple products.
     * 
     * @param minPrice minimum price for products to receive discount
     * @param maxPrice maximum price for products to receive discount
     * @param discountPercentage discount percentage to apply
     * @return list of updated products
     */
    public List<Product> applyBulkDiscount(BigDecimal minPrice, BigDecimal maxPrice, BigDecimal discountPercentage) {
        List<Product> productsInRange = productRepository.findByPriceBetween(minPrice, maxPrice);
        
        productsInRange.forEach(product -> {
            product.applyDiscount(discountPercentage);
            productRepository.save(product);
        });
        
        return productsInRange;
    }
    
    /**
     * Calculates the average price of all products.
     * This is a business calculation that requires aggregating data across products.
     * 
     * @return the average price of all products, or BigDecimal.ZERO if no products exist
     */
    public BigDecimal calculateAveragePrice() {
        List<Product> allProducts = productRepository.findAll();
        
        if (allProducts.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalPrice = allProducts.stream()
                .map(Product::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return totalPrice.divide(BigDecimal.valueOf(allProducts.size()), 2, java.math.RoundingMode.HALF_UP);
    }
    
    /**
     * Determines if a product is considered premium based on business rules.
     * A product is premium if it's expensive or in certain categories.
     * 
     * @param product the product to evaluate
     * @return true if the product is considered premium
     */
    public boolean isPremiumProduct(Product product) {
        return product.isExpensive() || 
               product.getCategory().name().equals("ELECTRONICS") ||
               product.getCategory().name().equals("AUTOMOTIVE");
    }
}
