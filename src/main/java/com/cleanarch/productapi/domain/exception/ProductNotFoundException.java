package com.cleanarch.productapi.domain.exception;

import com.cleanarch.productapi.domain.model.entity.ProductId;

/**
 * Exception thrown when a requested product cannot be found.
 * This represents a business scenario where a product doesn't exist.
 */
public class ProductNotFoundException extends DomainException {
    
    public ProductNotFoundException(ProductId productId) {
        super("Product with ID '" + productId.value() + "' not found");
    }
    
    public ProductNotFoundException(String message) {
        super(message);
    }
    
    public ProductNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
