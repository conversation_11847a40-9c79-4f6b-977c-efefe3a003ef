package com.cleanarch.productapi.domain.exception;

/**
 * Exception thrown when a product violates business rules or constraints.
 * This represents domain-level validation failures.
 */
public class InvalidProductException extends DomainException {
    
    public InvalidProductException(String message) {
        super(message);
    }
    
    public InvalidProductException(String message, Throwable cause) {
        super(message, cause);
    }
}
