package com.cleanarch.productapi.domain.exception;

/**
 * Exception thrown when attempting to create a product that already exists.
 * This represents a business rule violation for product uniqueness.
 */
public class DuplicateProductException extends DomainException {
    
    public DuplicateProductException(String message) {
        super(message);
    }
    
    public DuplicateProductException(String message, Throwable cause) {
        super(message, cause);
    }
}
