package com.cleanarch.productapi.domain.exception;

/**
 * Base exception class for all domain-related exceptions.
 * This provides a common base for all business rule violations and domain errors.
 */
public abstract class DomainException extends RuntimeException {
    
    protected DomainException(String message) {
        super(message);
    }
    
    protected DomainException(String message, Throwable cause) {
        super(message, cause);
    }
}
