package com.cleanarch.productapi.domain.model.entity;

import com.cleanarch.productapi.domain.exception.InvalidProductException;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Product domain entity representing a product in the business domain.
 * This is a rich domain model that encapsulates business rules and invariants.
 */
public class Product {
    
    private final ProductId id;
    private String name;
    private String description;
    private BigDecimal price;
    private Category category;
    private final LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Constructor for creating new products
    public Product(String name, String description, BigDecimal price, Category category) {
        this(null, name, description, price, category, LocalDateTime.now(), LocalDateTime.now());
    }
    
    // Constructor for reconstructing existing products (from persistence)
    public Product(ProductId id, String name, String description, BigDecimal price, 
                   Category category, LocalDateTime createdAt, LocalDateTime updatedAt) {
        this.id = id;
        this.createdAt = createdAt != null ? createdAt : LocalDateTime.now();
        this.updatedAt = updatedAt != null ? updatedAt : LocalDateTime.now();
        
        // Validate and set properties using business rules
        setName(name);
        setDescription(description);
        setPrice(price);
        setCategory(category);
    }
    
    public void updateDetails(String name, String description, BigDecimal price, Category category) {
        setName(name);
        setDescription(description);
        setPrice(price);
        setCategory(category);
        this.updatedAt = LocalDateTime.now();
    }
    
    private void setName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new InvalidProductException("Product name cannot be null or empty");
        }
        if (name.length() > 255) {
            throw new InvalidProductException("Product name cannot exceed 255 characters");
        }
        this.name = name.trim();
    }
    
    private void setDescription(String description) {
        this.description = description != null ? description.trim() : "";
    }
    
    private void setPrice(BigDecimal price) {
        if (price == null) {
            throw new InvalidProductException("Product price cannot be null");
        }
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidProductException("Product price cannot be negative");
        }
        this.price = price;
    }
    
    private void setCategory(Category category) {
        if (category == null) {
            throw new InvalidProductException("Product category cannot be null");
        }
        this.category = category;
    }
    
    // Business methods
    public boolean isExpensive() {
        return price.compareTo(new BigDecimal("1000")) > 0;
    }
    
    public void applyDiscount(BigDecimal discountPercentage) {
        if (discountPercentage == null || discountPercentage.compareTo(BigDecimal.ZERO) < 0 
            || discountPercentage.compareTo(new BigDecimal("100")) > 0) {
            throw new InvalidProductException("Discount percentage must be between 0 and 100");
        }
        
        BigDecimal discountAmount = price.multiply(discountPercentage).divide(new BigDecimal("100"));
        this.price = price.subtract(discountAmount);
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters
    public ProductId getId() {
        return id;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public Category getCategory() {
        return category;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Product product = (Product) o;
        return Objects.equals(id, product.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Product{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", price=" + price +
                ", category=" + category +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
