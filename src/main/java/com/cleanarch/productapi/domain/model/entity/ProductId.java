package com.cleanarch.productapi.domain.model.entity;

import java.util.UUID;

/**
 * Value object representing a Product identifier.
 * This follows Domain-Driven Design principles for strongly typed identifiers.
 */
public record ProductId(String value) {
    
    public ProductId {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("ProductId value cannot be null or empty");
        }
    }
    
    public static ProductId generate() {
        return new ProductId(UUID.randomUUID().toString());
    }
    
    public static ProductId of(String value) {
        return new ProductId(value);
    }
    
    public static ProductId of(Long value) {
        return new ProductId(value.toString());
    }
    
    @Override
    public String toString() {
        return value;
    }
}
