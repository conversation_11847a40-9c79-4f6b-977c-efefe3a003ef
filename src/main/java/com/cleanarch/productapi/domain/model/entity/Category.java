package com.cleanarch.productapi.domain.model.entity;

/**
 * Enumeration representing product categories.
 * This is a domain concept that defines the valid categories for products.
 */
public enum Category {
    ELECTRONICS("Electronics"),
    CLOTHING("Clothing"),
    BOOKS("Books"),
    HOME_GARDEN("Home & Garden"),
    SPORTS("Sports"),
    TOYS("Toys"),
    AUTOMOTIVE("Automotive"),
    HEALTH_BEAUTY("Health & Beauty"),
    FOOD_BEVERAGES("Food & Beverages"),
    OTHER("Other");
    
    private final String displayName;
    
    Category(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public static Category fromString(String value) {
        if (value == null) {
            return OTHER;
        }
        
        for (Category category : Category.values()) {
            if (category.name().equalsIgnoreCase(value) || 
                category.displayName.equalsIgnoreCase(value)) {
                return category;
            }
        }
        return OTHER;
    }
}
