package com.cleanarch.productapi.application.mapper;

import com.cleanarch.productapi.application.dto.request.CreateProductRequest;
import com.cleanarch.productapi.application.dto.response.ProductResponse;
import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.service.ProductDomainService;
import org.springframework.stereotype.Component;

/**
 * Mapper component for converting between domain entities and application DTOs.
 * This maintains the separation between layers by handling the transformation
 * of data structures at the application boundary.
 */
@Component
public class ProductMapper {
    
    /**
     * Maps a CreateProductRequest to a Product domain entity.
     * @param request the create product request
     * @return the product domain entity
     */
    public Product toDomain(CreateProductRequest request) {
        Category category = Category.fromString(request.category());
        
        return new Product(
                request.name(),
                request.description(),
                request.price(),
                category
        );
    }
    
    /**
     * Maps a Product domain entity to a ProductResponse.
     * @param product the product domain entity
     * @param domainService the domain service for additional business logic
     * @return the product response DTO
     */
    public ProductResponse toResponse(Product product, ProductDomainService domainService) {
        return new ProductResponse(
                product.getId() != null ? product.getId().value() : null,
                product.getName(),
                product.getDescription(),
                product.getPrice(),
                product.getCategory().name(),
                product.getCategory().getDisplayName(),
                domainService.isPremiumProduct(product),
                product.getCreatedAt(),
                product.getUpdatedAt()
        );
    }
}
