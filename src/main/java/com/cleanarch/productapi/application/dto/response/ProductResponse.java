package com.cleanarch.productapi.application.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Response DTO for product data.
 * This represents the output data structure for product information.
 */
public record ProductResponse(
        String id,
        String name,
        String description,
        BigDecimal price,
        String category,
        String categoryDisplayName,
        boolean isPremium,
        LocalDateTime createdAt,
        LocalDateTime updatedAt
) {
}
