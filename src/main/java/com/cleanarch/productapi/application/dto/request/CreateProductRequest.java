package com.cleanarch.productapi.application.dto.request;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * Request DTO for creating a new product.
 * This represents the input data required to create a product.
 */
public record CreateProductRequest(
        
        @NotBlank(message = "Product name is required")
        @Size(max = 255, message = "Product name cannot exceed 255 characters")
        String name,
        
        @Size(max = 1000, message = "Product description cannot exceed 1000 characters")
        String description,
        
        @NotNull(message = "Product price is required")
        @DecimalMin(value = "0.0", inclusive = true, message = "Product price cannot be negative")
        BigDecimal price,
        
        @NotBlank(message = "Product category is required")
        String category
) {
}
