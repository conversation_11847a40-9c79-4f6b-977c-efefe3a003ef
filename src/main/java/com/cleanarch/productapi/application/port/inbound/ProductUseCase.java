package com.cleanarch.productapi.application.port.inbound;

import com.cleanarch.productapi.application.dto.request.CreateProductRequest;
import com.cleanarch.productapi.application.dto.request.UpdateProductRequest;
import com.cleanarch.productapi.application.dto.response.ProductResponse;

import java.util.List;

/**
 * Inbound port (driving port) defining the use cases for product operations.
 * This interface represents the application's public API for product-related operations.
 * It follows the Ports and Adapters pattern by defining what the application can do
 * without specifying how it's done.
 */
public interface ProductUseCase {
    
    /**
     * Creates a new product.
     * @param request the product creation request
     * @return the created product response
     */
    ProductResponse createProduct(CreateProductRequest request);
    
    /**
     * Retrieves a product by its ID.
     * @param productId the product ID
     * @return the product response
     */
    ProductResponse getProductById(String productId);
    
    /**
     * Retrieves all products.
     * @return list of all product responses
     */
    List<ProductResponse> getAllProducts();
    
    /**
     * Updates an existing product.
     * @param productId the product ID to update
     * @param request the product update request
     * @return the updated product response
     */
    ProductResponse updateProduct(String productId, UpdateProductRequest request);
    
    /**
     * Deletes a product by its ID.
     * @param productId the product ID to delete
     */
    void deleteProduct(String productId);
    
    /**
     * Searches for products by name.
     * @param name the name to search for
     * @return list of matching product responses
     */
    List<ProductResponse> searchProductsByName(String name);
    
    /**
     * Retrieves products by category.
     * @param category the category to filter by
     * @return list of product responses in the specified category
     */
    List<ProductResponse> getProductsByCategory(String category);
}
