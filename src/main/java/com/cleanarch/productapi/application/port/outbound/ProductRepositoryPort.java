package com.cleanarch.productapi.application.port.outbound;

import com.cleanarch.productapi.domain.model.entity.Category;
import com.cleanarch.productapi.domain.model.entity.Product;
import com.cleanarch.productapi.domain.model.entity.ProductId;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Outbound port (driven port) for product repository operations.
 * This interface defines the contract for persistence operations that the application needs.
 * It follows the Dependency Inversion Principle by allowing the application layer
 * to define what it needs from the infrastructure layer.
 */
public interface ProductRepositoryPort {
    
    /**
     * Save a product to the repository.
     * @param product the product to save
     * @return the saved product with generated ID if it was a new product
     */
    Product save(Product product);
    
    /**
     * Find a product by its ID.
     * @param id the product ID
     * @return an Optional containing the product if found, empty otherwise
     */
    Optional<Product> findById(ProductId id);
    
    /**
     * Find all products.
     * @return list of all products
     */
    List<Product> findAll();
    
    /**
     * Find products by category.
     * @param category the product category
     * @return list of products in the specified category
     */
    List<Product> findByCategory(Category category);
    
    /**
     * Find products by name containing the specified text (case-insensitive).
     * @param name the name to search for
     * @return list of products with names containing the specified text
     */
    List<Product> findByNameContaining(String name);
    
    /**
     * Find products with price between the specified range.
     * @param minPrice minimum price (inclusive)
     * @param maxPrice maximum price (inclusive)
     * @return list of products within the price range
     */
    List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Delete a product by its ID.
     * @param id the product ID
     * @return true if the product was deleted, false if it didn't exist
     */
    boolean deleteById(ProductId id);
    
    /**
     * Check if a product exists by its ID.
     * @param id the product ID
     * @return true if the product exists, false otherwise
     */
    boolean existsById(ProductId id);
    
    /**
     * Count the total number of products.
     * @return the total count of products
     */
    long count();
}
