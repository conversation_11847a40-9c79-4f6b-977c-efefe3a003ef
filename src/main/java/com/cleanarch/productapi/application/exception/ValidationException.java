package com.cleanarch.productapi.application.exception;

import java.util.List;

/**
 * Exception thrown when application-level validation fails.
 * This represents validation errors that occur at the application boundary.
 */
public class ValidationException extends ApplicationException {
    
    private final List<String> validationErrors;
    
    public ValidationException(String message, List<String> validationErrors) {
        super(message);
        this.validationErrors = validationErrors;
    }
    
    public ValidationException(List<String> validationErrors) {
        super("Validation failed: " + String.join(", ", validationErrors));
        this.validationErrors = validationErrors;
    }
    
    public List<String> getValidationErrors() {
        return validationErrors;
    }
}
