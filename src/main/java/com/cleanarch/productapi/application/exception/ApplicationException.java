package com.cleanarch.productapi.application.exception;

/**
 * Base exception class for application-layer specific exceptions.
 * This provides a common base for application service errors and use case failures.
 */
public abstract class ApplicationException extends RuntimeException {
    
    protected ApplicationException(String message) {
        super(message);
    }
    
    protected ApplicationException(String message, Throwable cause) {
        super(message, cause);
    }
}
