-- Sample data for the Product API
-- This file is automatically executed by Spring Boot on application startup

INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('iPhone 15 Pro', 'Latest Apple smartphone with advanced camera system and A17 Pro chip', 999.99, 'ELECTRONICS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Samsung Galaxy S24', 'Premium Android smartphone with AI-powered features', 899.99, 'ELECTRONICS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('MacBook Air M3', 'Lightweight laptop with Apple M3 chip and all-day battery life', 1299.99, 'ELECTRONICS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Nike Air Max 270', 'Comfortable running shoes with Max Air cushioning', 150.00, 'SPORTS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Adidas Ultraboost 22', 'High-performance running shoes with Boost midsole', 180.00, 'SPORTS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Levi''s 501 Original Jeans', 'Classic straight-leg jeans in authentic indigo denim', 89.99, 'CLOTHING', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('The Clean Coder', 'A Code of Conduct for Professional Programmers by Robert C. Martin', 29.99, 'BOOKS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Design Patterns', 'Elements of Reusable Object-Oriented Software', 54.99, 'BOOKS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('KitchenAid Stand Mixer', 'Professional-grade stand mixer for baking and cooking', 379.99, 'HOME_GARDEN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Dyson V15 Detect', 'Cordless vacuum cleaner with laser dust detection', 749.99, 'HOME_GARDEN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('LEGO Creator Expert', 'Advanced building set for adult LEGO enthusiasts', 199.99, 'TOYS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Tesla Model Y Accessories', 'Premium accessories kit for Tesla Model Y', 299.99, 'AUTOMOTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Organic Green Tea', 'Premium organic green tea leaves from Japan', 24.99, 'FOOD_BEVERAGES', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Vitamin D3 Supplements', 'High-potency vitamin D3 for immune support', 19.99, 'HEALTH_BEAUTY', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
INSERT INTO products (name, description, price, category, created_at, updated_at) VALUES ('Wireless Bluetooth Headphones', 'Noise-cancelling over-ear headphones with 30-hour battery', 249.99, 'ELECTRONICS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
