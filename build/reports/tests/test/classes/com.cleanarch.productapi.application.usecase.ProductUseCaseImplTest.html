<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ProductUseCaseImplTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ProductUseCaseImplTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.cleanarch.productapi.application.usecase.html">com.cleanarch.productapi.application.usecase</a> &gt; ProductUseCaseImplTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">8</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.805s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">shouldCreateProductSuccessfully()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldDeleteProductSuccessfully()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldGetAllProductsSuccessfully()</td>
<td class="success">0.020s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldGetProductByIdSuccessfully()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldSearchProductsByNameSuccessfully()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldThrowExceptionWhenDeletingNonExistentProduct()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldThrowExceptionWhenProductNotFound()</td>
<td class="success">0.763s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">shouldUpdateProductSuccessfully()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>2025-07-28T18:58:51.292-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving product with ID: 999
2025-07-28T18:58:51.327-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving all products
2025-07-28T18:58:51.344-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Deleting product with ID: 999
2025-07-28T18:58:51.349-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Searching products by name: Test
2025-07-28T18:58:51.353-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Deleting product with ID: 1
2025-07-28T18:58:51.353-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product deleted successfully with ID: 1
2025-07-28T18:58:51.357-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Creating product with name: Test Product
2025-07-28T18:58:51.359-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product created successfully with ID: 1
2025-07-28T18:58:51.363-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Updating product with ID: 1
2025-07-28T18:58:51.364-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product updated successfully with ID: 1
2025-07-28T18:58:51.367-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving product with ID: 1
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at 28 jul. 2025 18:58:51</p>
</div>
</div>
</body>
</html>
