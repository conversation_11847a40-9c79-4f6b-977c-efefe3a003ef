<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">37</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">9</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.863s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">75%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Packages</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldCreateProductSuccessfully()">shouldCreateProductSuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldDeleteProductSuccessfully()">shouldDeleteProductSuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldFilterProductsByCategorySuccessfully()">shouldFilterProductsByCategorySuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldGetAllProductsSuccessfully()">shouldGetAllProductsSuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldGetProductByIdSuccessfully()">shouldGetProductByIdSuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldReturnNotFoundForNonExistentProduct()">shouldReturnNotFoundForNonExistentProduct()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldReturnValidationErrorForInvalidProduct()">shouldReturnValidationErrorForInvalidProduct()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldSearchProductsByNameSuccessfully()">shouldSearchProductsByNameSuccessfully()</a>
</li>
<li>
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">ProductControllerIntegrationTest</a>.
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html#shouldUpdateProductSuccessfully()">shouldUpdateProductSuccessfully()</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="packages/com.cleanarch.productapi.adapter.inbound.rest.controller.html">com.cleanarch.productapi.adapter.inbound.rest.controller</a>
</td>
<td>9</td>
<td>9</td>
<td>0</td>
<td>0.007s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.cleanarch.productapi.application.usecase.html">com.cleanarch.productapi.application.usecase</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.805s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.cleanarch.productapi.domain.model.entity.html">com.cleanarch.productapi.domain.model.entity</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.cleanarch.productapi.domain.service.html">com.cleanarch.productapi.domain.service</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.039s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="classes/com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest.html">com.cleanarch.productapi.adapter.inbound.rest.controller.ProductControllerIntegrationTest</a>
</td>
<td>9</td>
<td>9</td>
<td>0</td>
<td>0.007s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest.html">com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.805s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.cleanarch.productapi.domain.model.entity.ProductTest.html">com.cleanarch.productapi.domain.model.entity.ProductTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.cleanarch.productapi.domain.service.ProductDomainServiceTest.html">com.cleanarch.productapi.domain.service.ProductDomainServiceTest</a>
</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0.039s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at 28 jul. 2025 18:58:51</p>
</div>
</div>
</body>
</html>
