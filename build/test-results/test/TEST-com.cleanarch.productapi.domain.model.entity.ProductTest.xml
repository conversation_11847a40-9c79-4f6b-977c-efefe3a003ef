<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.cleanarch.productapi.domain.model.entity.ProductTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-07-28T23:58:51" hostname="alejocasta.local" time="0.016">
  <properties/>
  <testcase name="shouldReturnFalseForInexpensiveProduct()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldReturnTrueForExpensiveProduct()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldThrowExceptionWhenCategoryIsNull()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldThrowExceptionWhenNameIsNull()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.0"/>
  <testcase name="shouldThrowExceptionWhenPriceIsNegative()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldApplyDiscountCorrectly()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.002"/>
  <testcase name="shouldCreateValidProduct()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.003"/>
  <testcase name="shouldThrowExceptionWhenPriceIsNull()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldThrowExceptionForInvalidDiscount()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <testcase name="shouldUpdateProductDetails()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.0"/>
  <testcase name="shouldThrowExceptionWhenNameIsEmpty()" classname="com.cleanarch.productapi.domain.model.entity.ProductTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
