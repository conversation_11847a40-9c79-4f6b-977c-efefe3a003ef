<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" tests="9" skipped="0" failures="0" errors="0" timestamp="2025-07-28T23:58:51" hostname="alejocasta.local" time="0.041">
  <properties/>
  <testcase name="shouldCalculateAveragePriceCorrectly()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.026"/>
  <testcase name="shouldApplyBulkDiscountToProductsInPriceRange()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.003"/>
  <testcase name="shouldThrowExceptionWhenProductNameAlreadyExists()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.002"/>
  <testcase name="shouldIdentifyNonPremiumProduct()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.001"/>
  <testcase name="shouldReturnZeroAveragePriceWhenNoProducts()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.001"/>
  <testcase name="shouldValidateUniqueProductNameWhenNoExistingProducts()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.002"/>
  <testcase name="shouldAllowSimilarButNotExactProductNames()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.002"/>
  <testcase name="shouldIdentifyPremiumElectronicsProduct()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.001"/>
  <testcase name="shouldIdentifyPremiumExpensiveProduct()" classname="com.cleanarch.productapi.domain.service.ProductDomainServiceTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
