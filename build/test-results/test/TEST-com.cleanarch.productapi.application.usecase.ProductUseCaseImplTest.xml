<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-07-28T23:58:50" hostname="alejocasta.local" time="0.814">
  <properties/>
  <testcase name="shouldThrowExceptionWhenProductNotFound()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.763"/>
  <testcase name="shouldGetAllProductsSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.02"/>
  <testcase name="shouldThrowExceptionWhenDeletingNonExistentProduct()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.003"/>
  <testcase name="shouldSearchProductsByNameSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.004"/>
  <testcase name="shouldDeleteProductSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.003"/>
  <testcase name="shouldCreateProductSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.004"/>
  <testcase name="shouldUpdateProductSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.005"/>
  <testcase name="shouldGetProductByIdSuccessfully()" classname="com.cleanarch.productapi.application.usecase.ProductUseCaseImplTest" time="0.003"/>
  <system-out><![CDATA[2025-07-28T18:58:51.292-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving product with ID: 999
2025-07-28T18:58:51.327-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving all products
2025-07-28T18:58:51.344-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Deleting product with ID: 999
2025-07-28T18:58:51.349-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Searching products by name: Test
2025-07-28T18:58:51.353-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Deleting product with ID: 1
2025-07-28T18:58:51.353-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product deleted successfully with ID: 1
2025-07-28T18:58:51.357-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Creating product with name: Test Product
2025-07-28T18:58:51.359-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product created successfully with ID: 1
2025-07-28T18:58:51.363-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Updating product with ID: 1
2025-07-28T18:58:51.364-05:00  INFO 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Product updated successfully with ID: 1
2025-07-28T18:58:51.367-05:00 DEBUG 41050 --- [clean-architecture-product-api] [    Test worker] c.c.p.a.usecase.ProductUseCaseImpl       : Retrieving product with ID: 1
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
